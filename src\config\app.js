/**
 * Application Configuration
 * 
 * This file contains configuration settings for the AureaVoice application.
 * Modify these settings to switch between development and production modes.
 */

// Detect if we're in development mode
const isDevelopment = import.meta.env.DEV || window.location.hostname === 'localhost';

const config = {
  // Development/Production mode detection
  isDevelopment,
  isProduction: !isDevelopment,

  // Accent Detection API Configuration
  accentDetection: {
    // Use mock mode in development, real API in production
    useMockMode: isDevelopment,
    
    // Real API endpoint
    apiEndpoint: 'http://localhost:8000/identify',
    
    // Mock configuration
    mock: {
      // Simulated API delay range (in milliseconds)
      minDelay: 1000,
      maxDelay: 3000,
      
      // Confidence score generation
      baseScore: 60,      // Base confidence percentage (60%)
      scoreRange: 25,     // Additional range (60-85%)
      variation: 20,      // Random variation (±10%)
      minScore: 30,       // Minimum possible score
      maxScore: 95        // Maximum possible score
    }
  },

  // Logging configuration
  logging: {
    // Enable detailed console logging in development
    enableConsoleLog: isDevelopment,
    
    // Log levels: 'error', 'warn', 'info', 'debug'
    logLevel: isDevelopment ? 'debug' : 'error'
  },

  // UI Configuration
  ui: {
    // Show development indicators
    showDevIndicators: isDevelopment
  }
};

export default config;
