import ResultView from '../views/ResultView.js';

/**
 * ResultPresenter - Manages the result page presentation logic
 * Coordinates between ResultView and accent detection results
 */
class ResultPresenter {
  constructor(resultData = null) {
    this.view = null;
    this.resultData = resultData;
  }

  init() {
    this.view = new ResultView(this.resultData);
    this.render();
  }

  render() {
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';

    const viewElement = this.view.render();
    appElement.appendChild(viewElement);
  }

  /**
   * Update the result data and refresh the view
   */
  updateResult(newResultData) {
    this.resultData = newResultData;
    if (this.view) {
      this.view.updateResult(newResultData);
    }
  }

  destroy() {
    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default ResultPresenter;
