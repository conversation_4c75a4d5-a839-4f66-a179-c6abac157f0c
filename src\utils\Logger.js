import config from '../config/app.js';

/**
 * Logger - Centralized logging utility with configurable levels
 * Respects application configuration for development vs production logging
 */
class Logger {
  constructor() {
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.currentLevel = this.levels[config.logging.logLevel] || this.levels.error;
    this.enabled = config.logging.enableConsoleLog;
  }

  /**
   * Log error messages (always shown)
   * @param {string} message - Error message
   * @param {...any} args - Additional arguments
   */
  error(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.error) {
      console.error(`❌ ${message}`, ...args);
    }
  }

  /**
   * Log warning messages
   * @param {string} message - Warning message
   * @param {...any} args - Additional arguments
   */
  warn(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.warn) {
      console.warn(`⚠️ ${message}`, ...args);
    }
  }

  /**
   * Log info messages
   * @param {string} message - Info message
   * @param {...any} args - Additional arguments
   */
  info(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.info) {
      console.log(`ℹ️ ${message}`, ...args);
    }
  }

  /**
   * Log debug messages (development only)
   * @param {string} message - Debug message
   * @param {...any} args - Additional arguments
   */
  debug(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.debug) {
      console.log(`🔍 ${message}`, ...args);
    }
  }

  /**
   * Log recording-related messages
   * @param {string} message - Recording message
   * @param {...any} args - Additional arguments
   */
  recording(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.info) {
      console.log(`🎙️ ${message}`, ...args);
    }
  }

  /**
   * Log microphone-related messages
   * @param {string} message - Microphone message
   * @param {...any} args - Additional arguments
   */
  microphone(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.info) {
      console.log(`🎤 ${message}`, ...args);
    }
  }

  /**
   * Log transition-related messages
   * @param {string} message - Transition message
   * @param {...any} args - Additional arguments
   */
  transition(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.debug) {
      console.log(`🔄 ${message}`, ...args);
    }
  }

  /**
   * Log mock-related messages
   * @param {string} message - Mock message
   * @param {...any} args - Additional arguments
   */
  mock(message, ...args) {
    if (this.enabled && this.currentLevel >= this.levels.info) {
      console.log(`🎭 ${message}`, ...args);
    }
  }
}

// Export singleton instance
export default new Logger();
